%NAME%
mingw-w64-x86_64-sqlite3

%VERSION%
3.50.1-1

%BASE%
mingw-w64-sqlite3

%DESC%
A C library that implements an SQL database engine (mingw-w64)

%URL%
https://www.sqlite.org/

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/5c250470/15506283514)

%SIZE%
21080962

%REASON%
1

%LICENSE%
PublicDomain

%VALIDATION%
sha256
pgp

%REPLACES%
mingw-w64-x86_64-sqlite-analyzer

%DEPENDS%
mingw-w64-x86_64-readline
mingw-w64-x86_64-zlib

%OPTDEPENDS%
mingw-w64-x86_64-tcl: for sqlite3_analyzer

%CONFLICTS%
mingw-w64-x86_64-sqlite-analyzer

%PROVIDES%
mingw-w64-x86_64-sqlite=3.50.1
mingw-w64-x86_64-sqlite-analyzer=3.50.1

%XDATA%
pkgtype=split

