%FILES%
mingw64/
mingw64/bin/
mingw64/bin/libcppdap.dll
mingw64/include/
mingw64/include/dap/
mingw64/include/dap/any.h
mingw64/include/dap/dap.h
mingw64/include/dap/future.h
mingw64/include/dap/io.h
mingw64/include/dap/network.h
mingw64/include/dap/optional.h
mingw64/include/dap/protocol.h
mingw64/include/dap/serialization.h
mingw64/include/dap/session.h
mingw64/include/dap/traits.h
mingw64/include/dap/typeinfo.h
mingw64/include/dap/typeof.h
mingw64/include/dap/types.h
mingw64/include/dap/variant.h
mingw64/lib/
mingw64/lib/cmake/
mingw64/lib/cmake/cppdap/
mingw64/lib/cmake/cppdap/cppdap-targets-release.cmake
mingw64/lib/cmake/cppdap/cppdap-targets.cmake
mingw64/lib/cmake/cppdap/cppdapConfig.cmake
mingw64/lib/cmake/cppdap/cppdapConfigVersion.cmake
mingw64/lib/libcppdap.dll.a
mingw64/share/
mingw64/share/licenses/
mingw64/share/licenses/cppdap/
mingw64/share/licenses/cppdap/LICENSE

