#!/usr/bin/env python3
"""
RNNoise C程序编译脚本
编译dump_features和denoise_training程序

使用方法:
python compile_rnnoise.py
"""

import os
import sys
import subprocess
import time

class RNNoiseCompiler:
    def __init__(self):
        """初始化编译器"""
        self.gcc_path = "rnnoise-main/env/MSYS2/mingw64/bin/gcc.exe"
        self.src_dir = "rnnoise-main/src"
        self.include_dir = "rnnoise-main/include"
        
        # 通用源文件
        self.common_sources = [
            "kiss_fft.c",
            "pitch.c", 
            "celt_lpc.c",
            "parse_lpcnet_weights.c",
            "rnnoise_tables.c"
        ]
        
        # dump_features特定源文件
        self.dump_features_sources = self.common_sources + [
            "dump_features.c",
            "nnet.c",
            "nnet_default.c"
        ]
        
        # denoise_training特定源文件  
        self.denoise_training_sources = self.common_sources + [
            "denoise.c",
            "rnn.c",
            "rnnoise_data.c"
        ]
        
    def check_environment(self):
        """检查编译环境"""
        print("检查编译环境...")
        
        # 检查GCC编译器
        if not os.path.exists(self.gcc_path):
            print(f"错误: 找不到GCC编译器 {self.gcc_path}")
            return False
        
        # 检查源文件目录
        if not os.path.exists(self.src_dir):
            print(f"错误: 找不到源文件目录 {self.src_dir}")
            return False
            
        # 检查头文件目录
        if not os.path.exists(self.include_dir):
            print(f"错误: 找不到头文件目录 {self.include_dir}")
            return False
        
        print("✓ 编译环境检查通过")
        return True
    
    def check_source_files(self, sources):
        """检查源文件是否存在"""
        missing_files = []
        for source in sources:
            full_path = os.path.join(self.src_dir, source)
            if not os.path.exists(full_path):
                missing_files.append(full_path)
        
        if missing_files:
            print("错误: 缺少源文件:")
            for file in missing_files:
                print(f"  - {file}")
            return False
        
        return True
    
    def compile_program(self, program_name, sources, extra_flags=None):
        """编译程序"""
        print(f"\n编译 {program_name}...")
        
        # 检查源文件
        if not self.check_source_files(sources):
            return False
        
        # 构建编译命令
        cmd = [
            self.gcc_path,
            "-Wall", "-W", "-O3", "-g",
            f"-I{self.include_dir}"
        ]
        
        # 添加额外标志
        if extra_flags:
            cmd.extend(extra_flags)
        
        # 添加输出文件
        output_file = f"{program_name}.exe"
        cmd.extend(["-o", output_file])
        
        # 添加源文件
        for source in sources:
            cmd.append(os.path.join(self.src_dir, source))
        
        # 添加数学库
        cmd.append("-lm")
        
        print("编译命令:")
        print(" ".join(cmd))
        print()
        
        try:
            # 运行编译
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
            compile_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✓ {program_name} 编译成功! (耗时: {compile_time:.2f}秒)")
                
                # 检查生成的文件
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    print(f"  生成文件: {output_file} ({file_size:,} 字节)")
                    return True
                else:
                    print(f"✗ 编译成功但未找到输出文件: {output_file}")
                    return False
            else:
                print(f"✗ {program_name} 编译失败!")
                if result.stderr:
                    print("错误信息:")
                    print(result.stderr)
                if result.stdout:
                    print("输出信息:")
                    print(result.stdout)
                return False
                
        except Exception as e:
            print(f"编译过程中出现异常: {e}")
            return False
    
    def test_program(self, program_name):
        """测试编译的程序"""
        exe_file = f"{program_name}.exe"
        if not os.path.exists(exe_file):
            print(f"✗ 程序文件不存在: {exe_file}")
            return False
        
        print(f"测试 {program_name}...")
        
        try:
            # 运行程序看是否能正常启动
            result = subprocess.run([f"./{exe_file}"], 
                                  capture_output=True, text=True, timeout=5)
            
            # dump_features和denoise_training在没有参数时应该显示用法或返回错误
            if result.returncode != 0:
                if "usage:" in result.stderr.lower() or "usage:" in result.stdout.lower():
                    print(f"✓ {program_name} 运行正常 (显示用法信息)")
                    return True
                else:
                    print(f"✓ {program_name} 可以运行 (返回码: {result.returncode})")
                    return True
            else:
                print(f"✓ {program_name} 运行正常")
                return True
                
        except subprocess.TimeoutExpired:
            print(f"✓ {program_name} 运行正常 (等待输入)")
            return True
        except Exception as e:
            print(f"✗ {program_name} 测试失败: {e}")
            return False
    
    def compile_all(self):
        """编译所有程序"""
        print("RNNoise C程序编译器")
        print("=" * 50)
        
        # 检查环境
        if not self.check_environment():
            return False
        
        success_count = 0
        total_programs = 2
        
        # 编译dump_features
        if self.compile_program("dump_features", self.dump_features_sources):
            if self.test_program("dump_features"):
                success_count += 1
        
        # 编译denoise_training
        if self.compile_program("denoise_training", self.denoise_training_sources, 
                               extra_flags=["-DTRAINING=1"]):
            if self.test_program("denoise_training"):
                success_count += 1
        
        # 总结
        print("\n" + "=" * 50)
        print("编译总结")
        print("=" * 50)
        
        print(f"成功编译: {success_count}/{total_programs} 个程序")
        
        if success_count == total_programs:
            print("\n🎉 所有程序编译成功!")
            print("\n生成的程序:")
            print("- dump_features.exe: 特征提取工具")
            print("- denoise_training.exe: 训练数据生成工具")
            
            print("\n使用方法:")
            print("dump_features.exe <speech> <noise> <fg_noise> <output> <count>")
            print("denoise_training.exe (用于训练)")
            
            return True
        else:
            print(f"\n⚠️ 有 {total_programs - success_count} 个程序编译失败")
            print("请检查错误信息并修复问题")
            return False

def main():
    """主函数"""
    compiler = RNNoiseCompiler()
    success = compiler.compile_all()
    
    if success:
        print("\n下一步:")
        print("1. 使用dump_features.exe提取音频特征")
        print("2. 使用denoise_training.exe进行训练")
        print("3. 处理PCM数据: python process_pcm_data.py")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
