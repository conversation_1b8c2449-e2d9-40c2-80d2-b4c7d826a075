%NAME%
mingw-w64-x86_64-curl

%VERSION%
8.14.1-1

%BASE%
mingw-w64-curl

%DESC%
Command line tool and library for transferring data with URLs (mingw-w64)

%URL%
https://curl.se/

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/5c250470/15441901521)

%SIZE%
3988229

%REASON%
1

%LICENSE%
spdx:MIT

%VALIDATION%
sha256
pgp

%REPLACES%
mingw-w64-x86_64-wcurl

%DEPENDS%
mingw-w64-x86_64-cc-libs
mingw-w64-x86_64-c-ares
mingw-w64-x86_64-brotli
mingw-w64-x86_64-libidn2
mingw-w64-x86_64-libpsl
mingw-w64-x86_64-zlib
mingw-w64-x86_64-zstd
mingw-w64-x86_64-ca-certificates
mingw-w64-x86_64-libssh2
mingw-w64-x86_64-openssl
mingw-w64-x86_64-nghttp2
mingw-w64-x86_64-ngtcp2
mingw-w64-x86_64-nghttp3

%CONFLICTS%
mingw-w64-x86_64-curl-winssl
mingw-w64-x86_64-curl-gnutls
mingw-w64-x86_64-wcurl

%PROVIDES%
mingw-w64-x86_64-wcurl

%XDATA%
pkgtype=split

