%FILES%
mingw64/
mingw64/bin/
mingw64/bin/libisl-23.dll
mingw64/include/
mingw64/include/isl/
mingw64/include/isl/aff.h
mingw64/include/isl/aff_type.h
mingw64/include/isl/arg.h
mingw64/include/isl/ast.h
mingw64/include/isl/ast_build.h
mingw64/include/isl/ast_type.h
mingw64/include/isl/constraint.h
mingw64/include/isl/cpp.h
mingw64/include/isl/ctx.h
mingw64/include/isl/fixed_box.h
mingw64/include/isl/flow.h
mingw64/include/isl/hash.h
mingw64/include/isl/hmap.h
mingw64/include/isl/hmap_templ.c
mingw64/include/isl/id.h
mingw64/include/isl/id_to_ast_expr.h
mingw64/include/isl/id_to_id.h
mingw64/include/isl/id_to_pw_aff.h
mingw64/include/isl/id_type.h
mingw64/include/isl/ilp.h
mingw64/include/isl/list.h
mingw64/include/isl/local_space.h
mingw64/include/isl/lp.h
mingw64/include/isl/map.h
mingw64/include/isl/map_to_basic_set.h
mingw64/include/isl/map_type.h
mingw64/include/isl/mat.h
mingw64/include/isl/maybe.h
mingw64/include/isl/maybe_ast_expr.h
mingw64/include/isl/maybe_basic_set.h
mingw64/include/isl/maybe_id.h
mingw64/include/isl/maybe_pw_aff.h
mingw64/include/isl/maybe_templ.h
mingw64/include/isl/multi.h
mingw64/include/isl/obj.h
mingw64/include/isl/options.h
mingw64/include/isl/point.h
mingw64/include/isl/polynomial.h
mingw64/include/isl/polynomial_type.h
mingw64/include/isl/printer.h
mingw64/include/isl/printer_type.h
mingw64/include/isl/schedule.h
mingw64/include/isl/schedule_node.h
mingw64/include/isl/schedule_type.h
mingw64/include/isl/set.h
mingw64/include/isl/set_type.h
mingw64/include/isl/space.h
mingw64/include/isl/space_type.h
mingw64/include/isl/stdint.h
mingw64/include/isl/stream.h
mingw64/include/isl/stride_info.h
mingw64/include/isl/typed_cpp.h
mingw64/include/isl/union_map.h
mingw64/include/isl/union_map_type.h
mingw64/include/isl/union_set.h
mingw64/include/isl/union_set_type.h
mingw64/include/isl/val.h
mingw64/include/isl/val_gmp.h
mingw64/include/isl/val_type.h
mingw64/include/isl/vec.h
mingw64/include/isl/version.h
mingw64/include/isl/vertices.h
mingw64/lib/
mingw64/lib/libisl.a
mingw64/lib/libisl.dll.a
mingw64/lib/libisl.dll.a-gdb.py
mingw64/lib/pkgconfig/
mingw64/lib/pkgconfig/isl.pc

