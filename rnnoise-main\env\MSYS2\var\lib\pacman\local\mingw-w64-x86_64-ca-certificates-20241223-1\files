%FILES%
mingw64/
mingw64/bin/
mingw64/bin/update-ca-trust
mingw64/etc/
mingw64/etc/pki/
mingw64/etc/pki/ca-trust/
mingw64/etc/pki/ca-trust/extracted/
mingw64/etc/pki/ca-trust/extracted/java/
mingw64/etc/pki/ca-trust/extracted/java/cacerts
mingw64/etc/pki/ca-trust/extracted/openssl/
mingw64/etc/pki/ca-trust/extracted/openssl/ca-bundle.trust.crt
mingw64/etc/pki/ca-trust/extracted/pem/
mingw64/etc/pki/ca-trust/extracted/pem/email-ca-bundle.pem
mingw64/etc/pki/ca-trust/extracted/pem/objsign-ca-bundle.pem
mingw64/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem
mingw64/etc/pki/ca-trust/source/
mingw64/etc/pki/ca-trust/source/anchors/
mingw64/etc/pki/ca-trust/source/blacklist/
mingw64/etc/ssl/
mingw64/etc/ssl/cert.pem
mingw64/etc/ssl/certs/
mingw64/etc/ssl/certs/ca-bundle.crt
mingw64/etc/ssl/certs/ca-bundle.trust.crt
mingw64/lib/
mingw64/lib/p11-kit/
mingw64/lib/p11-kit/p11-kit-extract-trust
mingw64/share/
mingw64/share/man/
mingw64/share/man/man8/
mingw64/share/man/man8/update-ca-trust.8.gz
mingw64/share/pki/
mingw64/share/pki/ca-trust-legacy/
mingw64/share/pki/ca-trust-legacy/ca-bundle.legacy.default.crt
mingw64/share/pki/ca-trust-legacy/ca-bundle.legacy.disable.crt
mingw64/share/pki/ca-trust-source/
mingw64/share/pki/ca-trust-source/ca-bundle.trust.crt

