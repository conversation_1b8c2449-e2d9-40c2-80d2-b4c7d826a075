%NAME%
mingw-w64-x86_64-libidn2

%VERSION%
2.3.8-2

%BASE%
mingw-w64-libidn2

%DESC%
Implementation of the Stringprep, Punycode and IDNA specifications (mingw-w64)

%URL%
https://www.gnu.org/software/libidn/#libidn2

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/8d9cbcb5/14402684935)

%SIZE%
769915

%REASON%
1

%LICENSE%
spdx:GPL-2.0-or-later
spdx:LGPL-3.0-or-later

%VALIDATION%
sha256
pgp

%DEPENDS%
mingw-w64-x86_64-gettext-runtime
mingw-w64-x86_64-libunistring

%XDATA%
pkgtype=pkg

