%NAME%
mingw-w64-x86_64-python

%VERSION%
3.12.11-1

%BASE%
mingw-w64-python

%DESC%
A high-level scripting language (mingw-w64)

%URL%
https://www.python.org/

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/5c250470/15507745648)

%SIZE%
194700853

%REASON%
1

%LICENSE%
spdx:PSF-2.0

%VALIDATION%
sha256
pgp

%REPLACES%
mingw-w64-x86_64-python3
mingw-w64-x86_64-python3.12

%DEPENDS%
mingw-w64-x86_64-cc-libs
mingw-w64-x86_64-expat
mingw-w64-x86_64-bzip2
mingw-w64-x86_64-libffi
mingw-w64-x86_64-mpdecimal
mingw-w64-x86_64-ncurses
mingw-w64-x86_64-openssl
mingw-w64-x86_64-sqlite3
mingw-w64-x86_64-tcl
mingw-w64-x86_64-tk
mingw-w64-x86_64-zlib
mingw-w64-x86_64-xz
mingw-w64-x86_64-tzdata

%CONFLICTS%
mingw-w64-x86_64-python3
mingw-w64-x86_64-python3.12
mingw-w64-x86_64-python2<2.7.16-7

%PROVIDES%
mingw-w64-x86_64-python3
mingw-w64-x86_64-python3.12

%XDATA%
pkgtype=pkg

