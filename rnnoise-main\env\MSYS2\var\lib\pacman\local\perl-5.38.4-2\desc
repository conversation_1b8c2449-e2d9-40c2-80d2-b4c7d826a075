%NAME%
perl

%VERSION%
5.38.4-2

%BASE%
perl

%DESC%
A highly capable, feature-rich programming language

%URL%
https://www.perl.org/

%ARCH%
x86_64

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/e8d10d7e/14560701995)

%SIZE%
46505241

%REASON%
1

%LICENSE%
GPL

%VALIDATION%
pgp

%REPLACES%
perl-Scalar-List-Utils

%DEPENDS%
db
gdbm
libxcrypt
coreutils
sh

%PROVIDES%
perl-Archive-Tar=2.40
perl-Attribute-Handlers=1.03
perl-AutoLoader=5.74
perl-CPAN-Meta-Requirements=2.140
perl-CPAN-Meta-YAML=0.018
perl-CPAN-Meta=2.150010
perl-CPAN=2.36
perl-Carp=1.54
perl-Compress-Raw-Bzip2=2.204_001
perl-Compress-Raw-Zlib=2.204_001
perl-Config-Perl-V=0.36
perl-DB_File=1.858
perl-Data-Dumper=2.188
perl-Devel-PPPort=3.71
perl-Devel-SelfStubber=1.06
perl-Digest-MD5=2.58_01
perl-Digest-SHA=6.04
perl-Digest=1.20
perl-Dumpvalue=1.21
perl-Encode=3.19
perl-Env=1.06
perl-Exporter=5.77
perl-ExtUtils-CBuilder=0.280238
perl-ExtUtils-Constant=0.25
perl-ExtUtils-Install=2.22
perl-ExtUtils-MakeMaker=7.70
perl-ExtUtils-Manifest=1.73
perl-ExtUtils-PL2Bat=0.005
perl-ExtUtils-ParseXS=3.51
perl-File-Fetch=1.04
perl-File-Path=2.18
perl-File-Temp=0.2311
perl-Filter-Simple=0.96
perl-Filter-Util-Call=1.64
perl-FindBin=1.53
perl-Getopt-Long=2.54
perl-HTTP-Tiny=0.086
perl-I18N-Collate=1.02
perl-I18N-LangTags=0.45
perl-IO-Compress=2.204
perl-IO-Socket-IP=0.41_01
perl-IO-Zlib=1.14
perl-IO=1.52
perl-IPC-Cmd=1.04
perl-IPC-SysV=2.09
perl-JSON-PP=4.16
perl-Locale-Maketext-Simple=0.21_01
perl-Locale-Maketext=1.33
perl-MIME-Base64=3.16_01
perl-Math-BigInt-FastCalc=0.5013
perl-Math-BigInt=1.999837
perl-Math-BigRat=0.2624
perl-Math-Complex=1.62
perl-Memoize=1.16
perl-Module-CoreList=5.20231129
perl-Module-Load-Conditional=0.74
perl-Module-Load=0.36
perl-Module-Loaded=0.08
perl-Module-Metadata=1.000037
perl-NEXT=0.69
perl-Net-Ping=2.76
perl-Params-Check=0.38
perl-PathTools=3.89
perl-Perl-OSType=1.010
perl-PerlIO-via-QuotedPrint=0.10
perl-Pod-Checker=1.75
perl-Pod-Escapes=1.07
perl-Pod-Perldoc=3.2801
perl-Pod-Simple=3.43
perl-Pod-Usage=2.03
perl-Safe=2.44
perl-Scalar-List-Utils=1.63
perl-Search-Dict=1.07
perl-SelfLoader=1.26
perl-Socket=2.036
perl-Storable=3.32
perl-Sys-Syslog=0.36
perl-Term-ANSIColor=5.01
perl-Term-Cap=1.18
perl-Term-Complete=1.403
perl-Term-ReadLine=1.17
perl-Test-Harness=3.44
perl-Test-Simple=1.302194
perl-Test=1.31
perl-Text-Abbrev=1.02
perl-Text-Balanced=2.06
perl-Text-ParseWords=3.31
perl-Text-Tabs=2021.0814
perl-Thread-Queue=3.14
perl-Thread-Semaphore=2.13
perl-Tie-File=1.07
perl-Tie-RefHash=1.40
perl-Time-HiRes=1.9775
perl-Time-Local=1.30
perl-Time-Piece=1.3401_01
perl-Unicode-Collate=1.31
perl-Unicode-Normalize=1.32
perl-Win32=0.59
perl-Win32API-File=0.1203_01
perl-XSLoader=0.32
perl-autodie=2.36
perl-autouse=1.11
perl-base=2.27
perl-bignum=0.66
perl-constant=1.33
perl-encoding-warnings=0.14
perl-experimental=0.031
perl-if=0.0610
perl-lib=0.65
perl-libnet=3.15
perl-parent=0.241
perl-perlfaq=5.20210520
perl-podlators=5.010
perl-threads-shared=1.68
perl-threads=2.36
perl-version=0.9929

%XDATA%
pkgtype=split

