%NAME%
mingw-w64-x86_64-binutils

%VERSION%
2.44-4

%BASE%
mingw-w64-binutils

%DESC%
A set of programs to assemble and manipulate binary and object files (mingw-w64)

%URL%
https://www.gnu.org/software/binutils/

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/c237bc16/15642510678)

%SIZE%
44129484

%REASON%
1

%GROUPS%
mingw-w64-x86_64-toolchain

%LICENSE%
spdx:GPL-3.0-or-later AND GPL-2.0-or-later AND LGPL-3.0-or-later AND LGPL-2.0-or-later

%VALIDATION%
sha256
pgp

%DEPENDS%
mingw-w64-x86_64-gettext-runtime
mingw-w64-x86_64-libwinpthread
mingw-w64-x86_64-zlib
mingw-w64-x86_64-zstd

%XDATA%
pkgtype=pkg

